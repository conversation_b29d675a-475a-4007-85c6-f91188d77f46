import 'package:flutter/material.dart';

class GenerateQrCode extends StatefulWidget {
  const GenerateQrCode({super.key});

  @override
  State<GenerateQrCode> createState() => _GenerateQrCodeState();
}

class _GenerateQrCodeState extends State<GenerateQrCode> {
  TextEditingController urlController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Generate QR Code'), backgroundColor: Colors.blue,),
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if(urlController.text.isNotEmpty)
              QrImage(data: urlController.text, size: 200,),
              SizedBox(height: 10,),
              Container( 
                padding: EdgeInsets.only(left: 10, right: 10),
                child: <PERSON><PERSON><PERSON>(
                  controller: url<PERSON>ontroller,
                  decoration: InputDecoration(
                    hintText: 'Enter Your Data',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(15)),
                    LabelText: 'Enter Your',
                  ),
                ),
              )
              SizedBox(height: 10,),
              ElevatedButton(onPressed: (){
                setState(() {
                  
                });
              }, child: Text('Generate QR Code')),
            ],
          ),
        )
      ),
    );
  }
}
