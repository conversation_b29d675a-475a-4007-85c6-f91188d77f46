import 'package:flutter/material.dart';

class ScanQrCode extends StatefulWidget {
  const ScanQrCode({super.key});

  @override
  State<ScanQrCode> createState() => _ScanQrCodeState();
}

class _ScanQrCodeState extends State<ScanQrCode> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Scan QR Code'), backgroundColor: Colors.blue),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(height: 30),
            Text(data, style: TextStyle(color: Colors.black)),
            SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                setState(() {});
              },
              child: Text('Scan QR Code'),
            ),
          ],
        ),
      ),
    );
  }
}
